import 'package:equatable/equatable.dart';

/// <PERSON> p<PERSON>ł<PERSON> z internetem
abstract class ConnectivityState extends Equatable {
  const ConnectivityState();

  @override
  List<Object> get props => [];
}

/// Stan gdy połączenie jest dostępne
class ConnectivityConnected extends ConnectivityState {
  const ConnectivityConnected();
}

/// Stan gdy połączenie jest niedostępne
class ConnectivityDisconnected extends ConnectivityState {
  const ConnectivityDisconnected();
}

/// Stan pocz<PERSON>tkowy - nieznany stan połączenia
class ConnectivityInitial extends ConnectivityState {
  const ConnectivityInitial();
}