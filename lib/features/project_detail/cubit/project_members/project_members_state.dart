import 'package:equatable/equatable.dart';

import 'package:bandspace_mobile/shared/models/project_member.dart';

abstract class ProjectMembersState extends Equatable {
  const ProjectMembersState();

  @override
  List<Object?> get props => [];
}

class ProjectMembersInitial extends ProjectM<PERSON>bersState {
  const ProjectMembersInitial();
}

class ProjectMembersLoading extends ProjectMembersState {
  const ProjectMembersLoading();
}

class ProjectMembersLoadSuccess extends ProjectMembersState {
  final List<ProjectMember> members;

  const ProjectMembersLoadSuccess(this.members);

  @override
  List<Object?> get props => [members];
}

class ProjectMembersLoadFailure extends ProjectMembersState {
  final String message;

  const ProjectMembersLoadFailure(this.message);

  @override
  List<Object?> get props => [message];
}
