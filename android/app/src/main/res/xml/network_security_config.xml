<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- Konfiguracja tylko dla developmentu - localhost i IP lokalne -->
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">10.0.0.0/8</domain>
        <domain includeSubdomains="true">**********/12</domain>
        <domain includeSubdomains="true">***********/16</domain>
    </domain-config>
    
    <!-- Wszystkie inne domeny wymagają HTTPS -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>