{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "BandSpace (.env)",
      "request": "launch",
      "type": "dart",
      "program": "lib/main.dart"
    },
    {
      "name": "BandSpace (.env.local)",
      "request": "launch",
      "type": "dart",
      "program": "lib/main_local.dart"
    },
    {
      "name": "BandSpace (.env) - profile",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile",
      "program": "lib/main.dart"
    },
    {
      "name": "BandSpace (.env.local) - profile",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile",
      "program": "lib/main_local.dart"
    },
    {
      "name": "BandSpace (.env) - release",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release",
      "program": "lib/main.dart"
    }
  ]
}
